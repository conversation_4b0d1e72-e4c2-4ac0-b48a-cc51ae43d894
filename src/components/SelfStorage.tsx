'use client';

import { useState } from 'react';
import useScrollAnimation from '@/hooks/useScrollAnimation';
import { useLanguage } from '@/context/LanguageContext';

export default function SelfStorage() {
  const feature1Animation = useScrollAnimation<HTMLDivElement>();
  const feature2Animation = useScrollAnimation<HTMLDivElement>();
  const feature3Animation = useScrollAnimation<HTMLDivElement>();
  const { t, isRTL } = useLanguage();
  
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  const cardVariants = [
    {
      gradient: 'from-blue-500/20 via-purple-500/20 to-pink-500/20',
      hoverGradient: 'from-blue-500/30 via-purple-500/30 to-pink-500/30',
      iconBg: 'bg-gradient-to-br from-blue-500 to-purple-600',
      glowColor: 'shadow-blue-500/20',
      hoverGlow: 'group-hover:shadow-blue-500/40',
      borderColor: 'border-blue-500/30'
    },
    {
      gradient: 'from-green-500/20 via-teal-500/20 to-cyan-500/20',
      hoverGradient: 'from-green-500/30 via-teal-500/30 to-cyan-500/30',
      iconBg: 'bg-gradient-to-br from-green-500 to-teal-600',
      glowColor: 'shadow-green-500/20',
      hoverGlow: 'group-hover:shadow-green-500/40',
      borderColor: 'border-green-500/30'
    },
    {
      gradient: 'from-orange-500/20 via-red-500/20 to-pink-500/20',
      hoverGradient: 'from-orange-500/30 via-red-500/30 to-pink-500/30',
      iconBg: 'bg-gradient-to-br from-orange-500 to-red-600',
      glowColor: 'shadow-orange-500/20',
      hoverGlow: 'group-hover:shadow-orange-500/40',
      borderColor: 'border-orange-500/30'
    }
  ];

  const features = [
    {
      key: 'secure',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-12 h-12 text-white"
        >
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z" />
        </svg>
      ),
      animation: feature1Animation,
      delay: 'delay-100'
    },
    {
      key: 'recovery',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-12 h-12 text-white"
        >
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" />
        </svg>
      ),
      animation: feature2Animation,
      delay: 'delay-300'
    },
    {
      key: 'multichain',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-12 h-12 text-white"
        >
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
        </svg>
      ),
      animation: feature3Animation,
      delay: 'delay-500'
    }
  ];

  return (
    <section className="section-spacing relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-pink-400/10 to-orange-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-green-400/5 to-cyan-400/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="container-custom max-w-full px-4 relative z-10">
        {/* Mobile: Stack vertically, Desktop: Original horizontal layout */}
        <div className="feature-cards-container flex flex-col lg:flex-row lg:justify-center gap-8 lg:flex-nowrap lg:overflow-x-auto pb-8">
          {features.map((feature, index) => {
            const variant = cardVariants[index];
            const isHovered = hoveredCard === index;
            
            return (
              <div
                key={feature.key}
                ref={feature.animation.ref}
                className={`relative group fade-in-up ${feature.delay} w-full max-w-[392px] mx-auto lg:w-[392px] lg:min-w-[392px] h-[524px] lg:flex-shrink-0 transition-all duration-500 ease-out cursor-pointer ${feature.animation.isVisible ? 'visible' : ''}`}
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                {/* Glow effect */}
                <div 
                  className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${variant.gradient} opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl scale-105 ${variant.glowColor}`}
                ></div>
                
                {/* Card container */}
                <div 
                  className={`relative h-full bg-white/70 backdrop-blur-xl rounded-3xl p-8 border ${variant.borderColor} transition-all duration-500 group-hover:bg-white/80 group-hover:border-white/50 group-hover:shadow-2xl group-hover:scale-105 ${variant.hoverGlow}`}
                >
                  {/* Floating particles */}
                  <div className="absolute inset-0 overflow-hidden rounded-3xl">
                    {[...Array(6)].map((_, i) => (
                      <div
                        key={i}
                        className={`absolute w-2 h-2 bg-gradient-to-r ${variant.gradient} rounded-full opacity-30 transition-all duration-1000 ${isHovered ? 'animate-bounce' : ''}`}
                        style={{
                          left: `${20 + i * 15}%`,
                          top: `${10 + i * 12}%`,
                          animationDelay: `${i * 0.2}s`,
                          animationDuration: `${2 + i * 0.5}s`
                        }}
                      ></div>
                    ))}
                  </div>

                  <div className="flex flex-col items-center h-full justify-center px-4 relative z-10">
                    {/* Enhanced Icon Container */}
                    <div 
                      className={`relative w-24 h-24 ${variant.iconBg} rounded-2xl flex items-center justify-center mb-8 transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 shadow-lg`}
                    >
                      {/* Icon glow */}
                      <div className={`absolute inset-0 ${variant.iconBg} rounded-2xl blur-md opacity-50 group-hover:opacity-70 transition-opacity duration-500`}></div>
                      
                      {/* Icon */}
                      <div className="relative z-10 transition-transform duration-500 group-hover:scale-110">
                        {feature.icon}
                      </div>
                      
                      {/* Floating ring */}
                      <div className="absolute inset-0 border-2 border-white/30 rounded-2xl transition-all duration-500 group-hover:scale-125 group-hover:rotate-180"></div>
                    </div>

                    {/* Enhanced Title */}
                    <h3 className={`text-3xl font-bold mb-6 text-center transition-all duration-500 group-hover:scale-105 ${isRTL ? 'rtl-card-title font-tajawal' : ''}`}>
                      <span className="bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent group-hover:from-gray-900 group-hover:to-gray-700 transition-all duration-500">
                        {t(`feature.${feature.key}.title`)}
                      </span>
                    </h3>

                    {/* Enhanced Description */}
                    <p className={`text-lg text-gray-600 text-center leading-relaxed transition-all duration-500 group-hover:text-gray-800 ${isRTL ? 'rtl-card-text font-tajawal' : ''}`}>
                      {t(`feature.${feature.key}.description`)}
                    </p>

                    {/* Hover accent line */}
                    <div className={`mt-6 h-1 w-0 bg-gradient-to-r ${variant.gradient} rounded-full transition-all duration-500 group-hover:w-20`}></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
