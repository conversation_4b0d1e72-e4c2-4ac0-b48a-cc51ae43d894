'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useLanguage } from '@/context/LanguageContext';

export default function Footer() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;
  
  return (
    <footer className="bg-primary/10 py-12">
      <div className="container-custom">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-1">
            <Link href="/" className="flex items-center mb-4">
              <Image
                src="/logo.svg"
                alt="Mokhba Logo"
                width={80}
                height={80}
                className="mr-2"
                unoptimized
              />
            </Link>
            <p className="text-gray-700 mb-4">
              {t('footer.description')}
            </p>
            <div className="flex" style={{ gap: '1.5rem' }}>
              {/* LinkedIn */}
              <a href="https://www.linkedin.com/company/mokhba" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-accent transition">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
              {/* X (formerly Twitter) */}
              <a href="https://twitter.com/mokhbawallet" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-accent transition">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                </svg>
              </a>

            </div>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-lg font-bold mb-4 text-primary">{t('footer.company')}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${locale}/about`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.about')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/terms`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.terms')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/privacy`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.privacy')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/status`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.status')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Products Links */}
          <div>
            <h3 className="text-lg font-bold mb-4 text-primary">{t('footer.products')}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${locale}/app`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.download')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/security`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.security')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/support`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.support')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/feature-request`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.feature')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h3 className="text-lg font-bold mb-4 text-primary">{t('footer.resources')}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${locale}/explore`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.explore')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/learn`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.learn')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/blog`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.blog')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/docs`} className="text-gray-700 hover:text-primary transition">
                  {t('footer.docs')}
                </Link>
              </li>
            </ul>
          </div>

        </div>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-center text-gray-600">
            &copy; {new Date().getFullYear()} mokhba
          </p>
        </div>
      </div>
    </footer>
  );
}
