@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 115, 174, 210;
  --animation-duration: 0.8s;
}

body {
  color: rgb(var(--foreground-rgb));
  overflow-x: hidden;
  scroll-behavior: smooth;
  min-height: 100vh;
}

/* Material Icons - Ensure they always use the correct font */
.material-symbols-outlined {
  font-family: 'Material Symbols Outlined' !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 24px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  font-feature-settings: 'liga' !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
}

/* Optimize for animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Scroll Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Animation Delay Classes */
.delay-100 {
  transition-delay: 100ms;
}

.delay-200 {
  transition-delay: 200ms;
}

.delay-300 {
  transition-delay: 300ms;
}

.delay-400 {
  transition-delay: 400ms;
}

.delay-500 {
  transition-delay: 500ms;
}

/* RTL support for Arabic */
[dir="rtl"] {
  text-align: right;
  font-family: var(--font-tajawal), sans-serif;
}

/* Fix animation directions for RTL */
[dir="rtl"] .slide-in-left {
  transform: translateX(50px);
}

[dir="rtl"] .slide-in-left.visible {
  transform: translateX(0);
}

[dir="rtl"] .slide-in-right {
  transform: translateX(-50px);
}

[dir="rtl"] .slide-in-right.visible {
  transform: translateX(0);
}

/* Fix spacing and margins for RTL */
/* Fix for social icons in RTL */
[dir="rtl"] .flex[style*="gap"] {
  flex-direction: row;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

/* Fix button icon positioning for RTL */
[dir="rtl"] .btn-primary svg,
[dir="rtl"] .btn-secondary svg {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Fix text alignment for RTL */
[dir="rtl"] .text-center {
  text-align: center;
}

/* Fix flex direction for RTL - only for specific cases, not responsive layouts */
[dir="rtl"] .flex.rtl-row-only {
  flex-direction: row;
}

/* Ensure feature cards layout works correctly in RTL */
[dir="rtl"] .feature-cards-container {
  flex-direction: column !important;
}

@media (min-width: 1024px) {
  [dir="rtl"] .feature-cards-container {
    flex-direction: row !important;
  }
}

/* Ensure proper font for Arabic - EXCLUDING Material Icons */
[dir="rtl"] h1:not(.material-symbols-outlined),
[dir="rtl"] h2:not(.material-symbols-outlined),
[dir="rtl"] h3:not(.material-symbols-outlined),
[dir="rtl"] h4:not(.material-symbols-outlined),
[dir="rtl"] h5:not(.material-symbols-outlined),
[dir="rtl"] h6:not(.material-symbols-outlined),
[dir="rtl"] p:not(.material-symbols-outlined),
[dir="rtl"] span:not(.material-symbols-outlined),
[dir="rtl"] div:not(.material-symbols-outlined),
[dir="rtl"] button:not(.material-symbols-outlined),
[dir="rtl"] a:not(.material-symbols-outlined),
[dir="rtl"] input:not(.material-symbols-outlined),
[dir="rtl"] textarea:not(.material-symbols-outlined),
[dir="rtl"] select:not(.material-symbols-outlined) {
  font-family: var(--font-tajawal), sans-serif !important;
}

/* Ensure Material Icons are never overridden by RTL font rules */
[dir="rtl"] .material-symbols-outlined {
  font-family: 'Material Symbols Outlined' !important;
  direction: ltr !important;
}

/* RTL card styles */
[dir="rtl"] .flex-col {
  /* Ensure flex column direction is maintained in RTL */
  flex-direction: column !important;
}

.rtl-card-title {
  line-height: 1.5;
  padding: 0 8px;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
}

.rtl-card-text {
  line-height: 1.6;
  padding: 0 8px;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
}

/* ========================================
   STANDARDIZED BUTTON SYSTEM
   ========================================
   
   Button Sizes:
   - Large:  48px height, 16px font, 24px padding, 24px radius (pill) - Hero CTAs
   - Medium: 44px height, 16px font, 20px padding, 22px radius (pill) - Main actions
   - Small:  36px height, 14px font, 16px padding, 18px radius (pill) - Secondary actions
   
   Usage Examples:
   - Hero CTA: "btn-primary-large"
   - Form Submit: "btn-primary-medium"
   - Table Actions: "btn-ghost-small"
   - Secondary Actions: "btn-outline-medium"
   
   Available Variants:
   - Primary: btn-primary-{size}
   - Secondary: btn-secondary-{size}
   - Outline: btn-outline-{size}
   - Ghost: btn-ghost-{size}
   - Accent: btn-accent-{size}
   
   Works seamlessly for both LTR and RTL layouts
   ======================================== */

/* Base Button Styles */
.btn-base {
  @apply inline-flex items-center justify-center font-medium transition-all duration-200 cursor-pointer border-0 outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50 disabled:opacity-50 disabled:cursor-not-allowed;
  text-align: center;
  vertical-align: middle;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Button Sizes */
.btn-large {
  @apply btn-base text-base px-6 rounded-full;
  height: 48px;
  padding-left: 24px;
  padding-right: 24px;
  border-radius: 24px;
  font-size: 16px;
}

.btn-medium {
  @apply btn-base text-base px-5 rounded-full;
  height: 44px;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 22px;
  font-size: 16px;
}

.btn-small {
  @apply btn-base text-sm px-4 rounded-full;
  height: 36px;
  padding-left: 16px;
  padding-right: 16px;
  border-radius: 18px;
  font-size: 14px;
}

/* Button Color Variants - Primary */
.btn-primary {
  @apply bg-primary text-white hover:bg-primary/90 active:bg-primary/95 focus:ring-primary/50;
}

.btn-primary-large {
  @apply btn-large btn-primary;
}

.btn-primary-medium {
  @apply btn-medium btn-primary;
}

.btn-primary-small {
  @apply btn-small btn-primary;
}

/* Button Color Variants - Secondary */
.btn-secondary {
  @apply bg-secondary text-white hover:bg-secondary/90 active:bg-secondary/95 focus:ring-secondary/50;
}

.btn-secondary-large {
  @apply btn-large btn-secondary;
}

.btn-secondary-medium {
  @apply btn-medium btn-secondary;
}

.btn-secondary-small {
  @apply btn-small btn-secondary;
}

/* Button Color Variants - Outline */
.btn-outline {
  @apply border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-white active:bg-primary/95 focus:ring-primary/50;
}

.btn-outline-large {
  @apply btn-large btn-outline;
}

.btn-outline-medium {
  @apply btn-medium btn-outline;
}

.btn-outline-small {
  @apply btn-small btn-outline;
}

/* Button Color Variants - Ghost */
.btn-ghost {
  @apply text-gray-700 bg-transparent hover:bg-gray-100 active:bg-gray-200 focus:ring-gray-500/50;
}

.btn-ghost-large {
  @apply btn-large btn-ghost;
}

.btn-ghost-medium {
  @apply btn-medium btn-ghost;
}

.btn-ghost-small {
  @apply btn-small btn-ghost;
}

/* Button Color Variants - Accent */
.btn-accent {
  @apply bg-accent text-white hover:bg-accent/90 active:bg-accent/95 focus:ring-accent/50;
}

.btn-accent-large {
  @apply btn-large btn-accent;
}

.btn-accent-medium {
  @apply btn-medium btn-accent;
}

.btn-accent-small {
  @apply btn-small btn-accent;
}

/* Legacy Support - Keep existing classes working */
.btn-primary:not(.btn-large):not(.btn-medium):not(.btn-small) {
  @apply btn-base bg-primary text-white hover:bg-primary/90 active:bg-primary/95 focus:ring-primary/50 text-base px-5 rounded-full;
  height: 44px;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 22px;
  font-size: 16px;
}

.btn-secondary:not(.btn-large):not(.btn-medium):not(.btn-small) {
  @apply btn-base bg-secondary text-white hover:bg-secondary/90 active:bg-secondary/95 focus:ring-secondary/50 text-base px-5 rounded-full;
  height: 44px;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 22px;
  font-size: 16px;
}

.btn-outline:not(.btn-large):not(.btn-medium):not(.btn-small) {
  @apply btn-base border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-white active:bg-primary/95 focus:ring-primary/50 text-base px-5 rounded-full;
  height: 44px;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 22px;
  font-size: 16px;
}

.btn-accent:not(.btn-large):not(.btn-medium):not(.btn-small) {
  @apply btn-base bg-accent text-white hover:bg-accent/90 active:bg-accent/95 focus:ring-accent/50 text-base px-5 rounded-full;
  height: 44px;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 22px;
  font-size: 16px;
}

/* RTL Support for Buttons */
[dir="rtl"] .btn-base {
  text-align: center !important;
  direction: rtl;
}

[dir="rtl"] .btn-base .material-symbols-outlined {
  font-family: 'Material Symbols Outlined' !important;
  direction: ltr !important;
}

/* Ensure Arabic text is perfectly centered in buttons */
[dir="rtl"] .btn-base,
[dir="rtl"] .btn-large,
[dir="rtl"] .btn-medium,
[dir="rtl"] .btn-small {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  vertical-align: middle !important;
  line-height: 1.2 !important;
}

/* Fix Arabic font baseline alignment */
[dir="rtl"] .btn-base * {
  text-align: center;
  vertical-align: middle;
}

/* Icon spacing in buttons */
.btn-base .material-symbols-outlined + span,
.btn-base span + .material-symbols-outlined {
  margin-left: 8px;
}

[dir="rtl"] .btn-base .material-symbols-outlined + span,
[dir="rtl"] .btn-base span + .material-symbols-outlined {
  margin-left: 0;
  margin-right: 8px;
}

/* Arabic font-specific button adjustments */
.btn-base .font-tajawal,
[dir="rtl"] .btn-base {
  line-height: 1.1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* Ensure consistent text centering for all button text */
.btn-base,
.btn-large,
.btn-medium,
.btn-small {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-spacing {
  @apply py-16 md:py-24;
}

.heading-1 {
  @apply text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight;
}

.heading-2 {
  @apply text-2xl md:text-3xl lg:text-4xl font-bold leading-tight;
}

.heading-3 {
  @apply text-xl md:text-2xl font-bold leading-tight;
}

.paragraph {
  @apply text-base md:text-lg leading-relaxed;
}

/* Custom animations for enhanced iPhone mockup */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(1deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
  will-change: transform;
}

/* Enhance existing animations */
.animate-bounce {
  animation: bounce 2s infinite;
  will-change: transform;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -15px, 0);
  }
  70% {
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* ========================================
   ANIMATION SYSTEM
   ======================================== */

/* Optimized keyframe animations with GPU acceleration */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(1deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translate3d(-50px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translate3d(50px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -15px, 0);
  }
  70% {
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Performance-optimized animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
  will-change: transform;
}

.animate-bounce {
  animation: bounce 2s infinite;
  will-change: transform;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  will-change: transform, opacity;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
  will-change: transform, opacity;
}

.animate-fade-in-down {
  animation: fadeInDown 0.3s ease-out;
  will-change: transform, opacity;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
  will-change: transform, opacity;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
  will-change: transform, opacity;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
  will-change: transform, opacity;
}

/* Hardware acceleration helpers */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Performance optimized transitions */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-slow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scroll-based animation improvements */
.scroll-animation-element {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  contain: layout style paint;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-float,
  .animate-bounce,
  .animate-pulse {
    animation: none;
  }
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
  .bg-primary\/10,
  .bg-accent\/10 {
    opacity: 0.3;
  }
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Scroll Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
  will-change: transform, opacity;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Animation Delay Classes */
.delay-100 {
  transition-delay: 100ms;
}

.delay-200 {
  transition-delay: 200ms;
}

.delay-300 {
  transition-delay: 300ms;
}

.delay-400 {
  transition-delay: 400ms;
}

.delay-500 {
  transition-delay: 500ms;
}
