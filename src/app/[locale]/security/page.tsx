'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import { useLanguage } from '@/context/LanguageContext';

export default function SecurityPage() {
  const { t, isRTL } = useLanguage();
  const [activeTab, setActiveTab] = useState('features');
  
  // Security features data
  const securityFeatures = [
    {
      title: t('security.features.nonCustodial.title'),
      description: t('security.features.nonCustodial.description'),
      icon: 'key',
      color: 'bg-blue-500',
      details: [
        t('security.features.nonCustodial.details.1'),
        t('security.features.nonCustodial.details.2'),
        t('security.features.nonCustodial.details.3'),
        t('security.features.nonCustodial.details.4')
      ]
    },
    {
      title: t('security.features.encryption.title'),
      description: t('security.features.encryption.description'),
      icon: 'lock',
      color: 'bg-green-500',
      details: [
        t('security.features.encryption.details.1'),
        t('security.features.encryption.details.2'),
        t('security.features.encryption.details.3'),
        t('security.features.encryption.details.4')
      ]
    },
    {
      title: t('security.features.recovery.title'),
      description: t('security.features.recovery.description'),
      icon: 'backup',
      color: 'bg-purple-500',
      details: [
        t('security.features.recovery.details.1'),
        t('security.features.recovery.details.2'),
        t('security.features.recovery.details.3'),
        t('security.features.recovery.details.4')
      ]
    },
    {
      title: t('security.features.mfa.title'),
      description: t('security.features.mfa.description'),
      icon: 'verified_user',
      color: 'bg-orange-500',
      details: [
        t('security.features.mfa.details.1'),
        t('security.features.mfa.details.2'),
        t('security.features.mfa.details.3'),
        t('security.features.mfa.details.4')
      ]
    },
  ];

  // Security protocols
  const securityProtocols = [
    {
      title: t('security.protocols.auditing.title'),
      description: t('security.protocols.auditing.description'),
      icon: 'assignment_turned_in',
      status: t('security.status.active')
    },
    {
      title: t('security.protocols.bugBounty.title'),
      description: t('security.protocols.bugBounty.description'),
      icon: 'bug_report',
      status: t('security.status.active')
    },
    {
      title: t('security.protocols.penetration.title'),
      description: t('security.protocols.penetration.description'),
      icon: 'shield_person',
      status: t('security.status.quarterly')
    },
    {
      title: t('security.protocols.compliance.title'),
      description: t('security.protocols.compliance.description'),
      icon: 'gavel',
      status: t('security.status.continuous')
    }
  ];

  // Security metrics
  const securityMetrics = [
    { label: t('security.metrics.securityScore'), value: '99.8%', icon: 'security' },
    { label: t('security.metrics.uptime'), value: '99.99%', icon: 'schedule' },
    { label: t('security.metrics.incidents'), value: '0', icon: 'warning' },
    { label: t('security.metrics.responseTime'), value: '<1min', icon: 'speed' }
  ];

  const tabs = [
    { id: 'features', label: t('security.tabs.features'), icon: 'shield' },
    { id: 'protocols', label: t('security.tabs.protocols'), icon: 'policy' },
    { id: 'metrics', label: t('security.tabs.metrics'), icon: 'analytics' }
  ];

  return (
    <main className="relative bg-gradient-to-b from-white via-blue-50/30 to-[#73AED2]/20">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('security.title')}
          subtitle={t('security.subtitle')}
          icon="shield"
        >
          {/* Hero Security Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8 mb-12"
          >
            {securityMetrics.map((metric, index) => (
              <div key={index} className="bg-white/80 backdrop-blur-sm rounded-xl p-6 text-center shadow-lg border border-white/20">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                  <span className="material-symbols-outlined text-xl text-primary">
                    {metric.icon}
                  </span>
                </div>
                <div className="text-2xl font-bold text-blue-950 mb-1">{metric.value}</div>
                <div className={`text-sm text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>{metric.label}</div>
              </div>
            ))}
          </motion.div>

          {/* Enhanced Tab Navigation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col items-center mb-12"
          >
            {/* Tab Section Title */}
            <h2 className={`text-3xl font-bold text-blue-950 mb-8 text-center ${isRTL ? 'font-tajawal' : ''}`}>
              {t('security.overview')}
            </h2>
            
            {/* Modern Card-based Tab Navigation */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl">
              {tabs.map((tab, index) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  whileHover={{ scale: 1.05, y: -5 }}
                  whileTap={{ scale: 0.95 }}
                  className={`relative group p-6 rounded-2xl transition-all duration-500 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-br from-primary to-primary/80 text-white shadow-2xl shadow-primary/30'
                      : 'bg-white/80 backdrop-blur-sm text-blue-950 hover:bg-white/90 shadow-lg hover:shadow-xl border border-white/20'
                  }`}
                >
                  {/* Glowing effect for active tab */}
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="activeTabGlow"
                      className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl blur-xl"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                  
                  <div className="relative z-10">
                    {/* Icon with animated background */}
                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mb-4 mx-auto transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-white/20 backdrop-blur-sm'
                        : 'bg-primary/10 group-hover:bg-primary/20'
                    }`}>
                      <span className={`material-symbols-outlined text-3xl transition-all duration-300 ${
                        activeTab === tab.id
                          ? 'text-white'
                          : 'text-primary group-hover:scale-110'
                      }`}>
                        {tab.icon}
                      </span>
                    </div>
                    
                    {/* Tab Label */}
                    <h3 className={`text-lg font-bold mb-2 transition-all duration-300 ${isRTL ? 'font-tajawal' : ''}`}>
                      {tab.label}
                    </h3>
                    
                    {/* Tab Description */}
                    <p className={`text-sm opacity-80 ${isRTL ? 'font-tajawal' : ''}`}>
                      {tab.id === 'features' && t('security.tabs.features.description')}
                      {tab.id === 'protocols' && t('security.tabs.protocols.description')}
                      {tab.id === 'metrics' && t('security.tabs.metrics.description')}
                    </p>
                    
                    {/* Active indicator */}
                    {activeTab === tab.id && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-2 -right-2 w-6 h-6 bg-accent rounded-full flex items-center justify-center"
                      >
                        <span className="material-symbols-outlined text-white text-sm">check</span>
                      </motion.div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Enhanced Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 30, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -30, scale: 0.95 }}
            transition={{ duration: 0.6, type: "spring", bounce: 0.1 }}
            className="relative"
          >
            {/* Content Background with Gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-primary/5 to-accent/10 rounded-3xl blur-3xl"></div>
            
            <div className="relative z-10">
              {activeTab === 'features' && (
                <div className="space-y-8">
                  {/* Section Header */}
                  <div className="text-center mb-12">
                    <h3 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                      {t('security.advancedFeatures')}
                    </h3>
                    <p className={`text-blue-900/70 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
                      {t('security.advancedFeaturesDescription')}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {securityFeatures.map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 30, rotateX: -15 }}
                        animate={{ opacity: 1, y: 0, rotateX: 0 }}
                        transition={{ 
                          duration: 0.6, 
                          delay: 0.1 * index,
                          type: "spring",
                          bounce: 0.1
                        }}
                        whileHover={{ y: -8, scale: 1.02 }}
                        className="group relative bg-white/90 backdrop-blur-lg rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-500 border border-white/30 overflow-hidden"
                      >
                        {/* Animated background gradient */}
                        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-primary/5 to-accent/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        
                        {/* Floating particles effect */}
                        <div className="absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full animate-pulse"></div>
                        <div className="absolute bottom-8 left-6 w-1 h-1 bg-accent/40 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                        
                        <div className="relative z-10">
                          {/* Enhanced Icon with Glow */}
                          <div className="relative mb-6">
                            <div className={`w-20 h-20 rounded-3xl ${feature.color} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3`}>
                              <span className="material-symbols-outlined text-3xl text-white">
                                {feature.icon}
                              </span>
                            </div>
                            {/* Glow effect */}
                            <div className={`absolute inset-0 w-20 h-20 rounded-3xl ${feature.color} opacity-20 blur-xl group-hover:opacity-40 transition-opacity duration-500`}></div>
                          </div>
                          
                          <h3 className={`text-xl font-bold text-blue-950 mb-3 group-hover:text-primary transition-colors duration-300 ${isRTL ? 'font-tajawal' : ''}`}>
                            {feature.title}
                          </h3>
                          <p className={`text-blue-900/70 mb-6 leading-relaxed ${isRTL ? 'font-tajawal' : ''}`}>
                            {feature.description}
                          </p>
                          
                          {/* Enhanced Details List */}
                          <div className="space-y-3">
                            {feature.details.map((detail, idx) => (
                              <motion.div 
                                key={idx} 
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.1 * index + 0.1 * idx }}
                                className={`flex items-center text-sm text-blue-900/80 group-hover:text-blue-950 transition-colors duration-300 ${isRTL ? 'space-x-reverse space-x-3' : 'space-x-3'}`}
                              >
                                <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                                  <span className="material-symbols-outlined text-green-600 text-xs">check</span>
                                </div>
                                <span className={`${isRTL ? 'font-tajawal' : ''} group-hover:font-medium transition-all duration-300`}>{detail}</span>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                                     </div>
                </div>
              )}
            </div>

            {activeTab === 'protocols' && (
              <div className="space-y-8">
                {/* Section Header */}
                <div className="text-center mb-12">
                  <h3 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                    {t('security.protocolsProcesses')}
                  </h3>
                  <p className={`text-blue-900/70 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
                    {t('security.protocolsDescription')}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {securityProtocols.map((protocol, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30, rotateY: index % 2 === 0 ? -10 : 10 }}
                      animate={{ opacity: 1, x: 0, rotateY: 0 }}
                      transition={{ duration: 0.6, delay: 0.1 * index, type: "spring", bounce: 0.1 }}
                      whileHover={{ y: -5, scale: 1.02 }}
                      className="group relative bg-white/90 backdrop-blur-lg rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/30 overflow-hidden"
                    >
                      {/* Animated border gradient */}
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-transparent to-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
                      
                      {/* Status indicator glow */}
                      <div className="absolute top-4 right-4 w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                      
                      <div className="relative z-10">
                        <div className="flex items-start justify-between mb-6">
                          <div className="relative">
                            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="material-symbols-outlined text-2xl text-primary">
                                {protocol.icon}
                              </span>
                            </div>
                            <div className="absolute inset-0 w-16 h-16 rounded-2xl bg-primary/10 blur-xl group-hover:bg-primary/20 transition-colors duration-500"></div>
                          </div>
                          <div className="flex flex-col items-end">
                            <span className="bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 text-xs font-semibold px-3 py-1.5 rounded-full border border-green-200 shadow-sm">
                              {protocol.status}
                            </span>
                          </div>
                        </div>
                        
                        <h3 className={`text-xl font-bold text-blue-950 mb-3 group-hover:text-primary transition-colors duration-300 ${isRTL ? 'font-tajawal' : ''}`}>
                          {protocol.title}
                        </h3>
                        <p className={`text-blue-900/70 leading-relaxed ${isRTL ? 'font-tajawal' : ''}`}>
                          {protocol.description}
                        </p>
                        
                        {/* Progress indicator */}
                        <div className="mt-6 pt-4 border-t border-blue-100">
                          <div className="flex items-center justify-between text-sm">
                            <span className={`text-blue-900/60 ${isRTL ? 'font-tajawal' : ''}`}>{t('security.status.implementation')}</span>
                            <span className="font-semibold text-green-600">{t('security.status.complete')}</span>
                          </div>
                          <div className="mt-2 h-2 bg-blue-100 rounded-full overflow-hidden">
                            <motion.div 
                              className="h-full bg-gradient-to-r from-green-400 to-emerald-500"
                              initial={{ width: 0 }}
                              animate={{ width: '100%' }}
                              transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                            ></motion.div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'metrics' && (
              <div className="space-y-12">
                {/* Section Header */}
                <div className="text-center mb-12">
                  <h3 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                    {t('security.metricsMonitoring')}
                  </h3>
                  <p className={`text-blue-900/70 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
                    {t('security.metricsDescription')}
                  </p>
                </div>

                {/* Enhanced Real-time Security Dashboard */}
                <motion.div 
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="relative bg-white/90 backdrop-blur-lg rounded-3xl p-10 shadow-2xl border border-white/30 overflow-hidden"
                >
                  {/* Animated background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5"></div>
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 via-blue-500 to-purple-500"></div>
                  
                  <div className="relative z-10">
                    <h3 className={`text-3xl font-bold text-blue-950 mb-8 text-center ${isRTL ? 'font-tajawal' : ''}`}>
                      {t('security.dashboard.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                      {[
                        { icon: 'verified', value: '100%', label: t('security.dashboard.systemIntegrity'), color: 'green', delay: 0 },
                        { icon: 'monitoring', value: '24/7', label: t('security.dashboard.activeMonitoring'), color: 'blue', delay: 0.2 },
                        { icon: 'encrypted', value: '256-bit', label: t('security.dashboard.encryptionLevel'), color: 'purple', delay: 0.4 }
                      ].map((metric, index) => (
                        <motion.div 
                          key={index}
                          initial={{ opacity: 0, scale: 0.8, y: 20 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: metric.delay }}
                          className="text-center group"
                        >
                          <div className={`relative w-24 h-24 rounded-2xl bg-${metric.color}-100 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                            <span className={`material-symbols-outlined text-4xl text-${metric.color}-600`}>{metric.icon}</span>
                            {/* Pulse effect */}
                            <div className={`absolute inset-0 rounded-2xl bg-${metric.color}-200 opacity-0 group-hover:opacity-30 transition-opacity duration-300 animate-pulse`}></div>
                          </div>
                          <motion.div 
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: metric.delay + 0.3 }}
                            className={`text-4xl font-bold text-${metric.color}-600 mb-3`}
                          >
                            {metric.value}
                          </motion.div>
                          <div className={`text-sm font-medium text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>{metric.label}</div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>

                {/* Enhanced Security Certifications */}
                <motion.div 
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="relative bg-white/90 backdrop-blur-lg rounded-3xl p-10 shadow-2xl border border-white/30 overflow-hidden"
                >
                  {/* Decorative elements */}
                  <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-2xl"></div>
                  <div className="absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-accent/10 to-primary/10 rounded-full blur-xl"></div>
                  
                  <div className="relative z-10">
                    <h3 className={`text-3xl font-bold text-blue-950 mb-8 text-center ${isRTL ? 'font-tajawal' : ''}`}>
                      {t('security.certifications.title')}
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                      {['ISO 27001', 'SOC 2 Type II', 'GDPR Compliant', 'PCI DSS'].map((cert, index) => (
                        <motion.div 
                          key={index}
                          initial={{ opacity: 0, y: 20, rotateX: -15 }}
                          animate={{ opacity: 1, y: 0, rotateX: 0 }}
                          transition={{ duration: 0.5, delay: 0.1 * index }}
                          whileHover={{ y: -5, scale: 1.05 }}
                          className="group relative bg-gradient-to-br from-white via-primary/5 to-accent/5 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40"
                        >
                          {/* Badge glow effect */}
                          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          
                          <div className="relative z-10">
                            <div className="w-16 h-16 rounded-2xl bg-white shadow-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                              <span className="material-symbols-outlined text-2xl text-primary">award_star</span>
                            </div>
                            <div className={`text-sm font-bold text-blue-950 group-hover:text-primary transition-colors duration-300 ${isRTL ? 'font-tajawal' : ''}`}>
                              {cert}
                            </div>
                            
                            {/* Verification checkmark */}
                            <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <span className="material-symbols-outlined text-white text-xs">check</span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>
            )}
          </motion.div>

          {/* Enhanced Security Commitment Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-16 bg-gradient-to-r from-primary/10 via-white/50 to-accent/10 rounded-2xl p-8 border border-primary/20 shadow-lg"
          >
            <div className="flex items-start mb-6">
              <div className="w-16 h-16 rounded-2xl bg-primary flex items-center justify-center mr-6">
                <span className="material-symbols-outlined text-2xl text-white">security_key</span>
              </div>
              <div>
                <h2 className={`text-3xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('security.commitment.title')}
                </h2>
                <p className={`text-blue-900/70 mb-4 text-lg leading-relaxed ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('security.commitment.description1')}
                </p>
                <p className={`text-blue-900/70 text-lg leading-relaxed ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('security.commitment.description2')}
                </p>
              </div>
            </div>

            {/* Security Contact */}
            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 mt-6 border border-white/30">
              <h3 className={`text-xl font-bold text-blue-950 mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
                {t('security.responseTeam.title')}
              </h3>
              <p className={`text-blue-900/70 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                {t('security.responseTeam.description')}
              </p>
              <div className={`flex flex-wrap gap-4 ${isRTL ? 'space-x-reverse' : ''}`}>
                <a
                  href="mailto:<EMAIL>"
                  className="flex items-center bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                >
                  <span className="material-symbols-outlined mr-2">email</span>
                  <span className={isRTL ? 'font-tajawal' : ''}><EMAIL></span>
                </a>
                <button className="flex items-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                  <span className="material-symbols-outlined mr-2">report_problem</span>
                  <span className={isRTL ? 'font-tajawal' : ''}>{t('security.responseTeam.reportVulnerability')}</span>
                </button>
              </div>
            </div>
          </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
